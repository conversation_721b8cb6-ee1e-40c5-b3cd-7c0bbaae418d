# Route Migration Guide

This document outlines the routing refactoring implemented to establish consistent naming conventions across the DERENTAL platform. All changes maintain backward compatibility to ensure no disruption to existing functionality.

## Overview

The routing refactoring implements:

- **Kebab-case naming convention** for all routes
- **Consistent terminology** (provider instead of equipper, renter instead of lodger)
- **Logical route grouping** and hierarchy
- **RESTful API patterns** with proper HTTP verbs
- **Backward compatibility** through legacy route mappings

## Frontend Route Changes (src/App.jsx)

### Management Portals

| Legacy Route                | New Route                     | Description                   |
| --------------------------- | ----------------------------- | ----------------------------- |
| `/equipperManagementPortal` | `/provider-management-portal` | Provider management dashboard |
| `/renterManagementPortal`   | `/renter-management-portal`   | Renter management dashboard   |

### Nested Management Routes

| Legacy Route                   | New Route               | Description                   |
| ------------------------------ | ----------------------- | ----------------------------- |
| `bidzManagement`               | `bidz-management`       | Bidding management section    |
| `equipmentManagement`          | `equipment-management`  | Equipment management section  |
| `addEquipment`                 | `add-equipment`         | Add equipment form            |
| `addSingleEquipment`           | `add-single-equipment`  | Add single equipment form     |
| `equipmentDetails/:id`         | `equipment-details/:id` | Equipment details view        |
| `updateEquipment/:equipmentId` | `update-equipment/:id`  | Update equipment form         |
| `teamManagement`               | `team-management`       | Team management section       |
| `pricingAndMarketing`          | `pricing-and-marketing` | Pricing and marketing section |
| `projectManagement`            | `project-management`    | Project management section    |
| `rentalsSummary`               | `rentals-summary`       | Rentals summary view          |
| `creditCheckForm`              | `credit-check-form`     | Credit check form             |
| `commentsAndRatings`           | `comments-and-ratings`  | Comments and ratings section  |

### User Registration

| Legacy Route        | New Route             | Description           |
| ------------------- | --------------------- | --------------------- |
| `/signUpAsLodger`   | `/signup-as-renter`   | Renter registration   |
| `/signUpAsEquipper` | `/signup-as-provider` | Provider registration |

### Search and Results

| Legacy Route                | New Route                     | Description              |
| --------------------------- | ----------------------------- | ------------------------ |
| `/searchResult/:params`     | `/search-result/:params`      | Equipment search results |
| `/bidzSearchResult/:params` | `/bidz-search-result/:params` | Bidz search results      |

### Company Spotlights

| Legacy Route                   | New Route                       | Description             |
| ------------------------------ | ------------------------------- | ----------------------- |
| `/companySpotlight/:params`    | `/company-spotlight/:params`    | Company spotlight page  |
| `/equipperSpotlight/:userName` | `/provider-spotlight/:userName` | Provider spotlight page |

### Inventory Management

| Legacy Route                     | New Route                           | Description              |
| -------------------------------- | ----------------------------------- | ------------------------ |
| `/toolerBidzInventoryManagement` | `/tooler-bidz-inventory-management` | Tooler bidz inventory    |
| `/availableInventory`            | `/available-inventory`              | Available inventory view |

### Legal and Information Pages

| Legacy Route            | New Route                | Description            |
| ----------------------- | ------------------------ | ---------------------- |
| `/terms&conditions`     | `/terms-and-conditions`  | Terms and conditions   |
| `/privacyPolicy`        | `/privacy-policy`        | Privacy policy         |
| `/ourStory`             | `/our-story`             | Company story page     |
| `/equipmentDetails/:id` | `/equipment-details/:id` | Equipment details page |

## Backend API Route Changes (backend/api/server.go)

### Public Authentication Routes

| Legacy Route                         | New Route                                 | Description          |
| ------------------------------------ | ----------------------------------------- | -------------------- |
| `POST /public/signin`                | `POST /public/auth/signin`                | User sign in         |
| `POST /public/signup`                | `POST /public/auth/signup`                | User registration    |
| `GET /public/exist/:email`           | `GET /public/auth/user/exists/:email`     | Check user existence |
| `GET /public/password/forgot/:email` | `GET /public/auth/password/forgot/:email` | Forgot password      |
| `POST /public/password/validate`     | `POST /public/auth/password/validate`     | Validate reset token |
| `POST /public/password/reset`        | `POST /public/auth/password/reset`        | Reset password       |

### Public Equipment Routes

| Legacy Route                          | New Route                           | Description               |
| ------------------------------------- | ----------------------------------- | ------------------------- |
| `GET /public/equipment/:equipment_id` | `GET /public/equipment/:id`         | Get equipment details     |
| `GET /public/equipment/equipper`      | `GET /public/equipment/by-provider` | Get equipment by provider |

### Public Provider Routes (formerly Equipper)

| Legacy Route                               | New Route                                   | Description               |
| ------------------------------------------ | ------------------------------------------- | ------------------------- |
| `GET /public/equipper/:equipper_id`        | `GET /public/provider/:id`                  | Get provider by ID        |
| `GET /public/equipper/email/:email`        | `GET /public/provider/email/:email`         | Get provider by email     |
| `GET /public/equipper/user_name/:userName` | `GET /public/provider/username/:username`   | Get provider by username  |
| `GET /public/equipper/category`            | `GET /public/provider/by-category`          | Get providers by category |
| `GET /public/equippers`                    | `GET /public/providers`                     | Get all providers         |
| `GET /public/equippers/country/:country`   | `GET /public/providers/by-country/:country` | Get providers by country  |

### Public Miscellaneous Routes

| Legacy Route                                                  | New Route                               | Description               |
| ------------------------------------------------------------- | --------------------------------------- | ------------------------- |
| `GET /public/tooler_bidz_equipment/:tooler_bidz_equipment_id` | `GET /public/tooler-bidz-equipment/:id` | Get tooler bidz equipment |
| `GET /public/country`                                         | `GET /public/countries`                 | Get all countries         |

### Authenticated Provider Routes

| Legacy Route                      | New Route                             | Description            |
| --------------------------------- | ------------------------------------- | ---------------------- |
| `GET /equipper`                   | `GET /provider`                       | Get current provider   |
| `PUT /equipper`                   | `PUT /provider`                       | Update provider        |
| `DELETE /equipper`                | `DELETE /provider`                    | Delete provider        |
| `POST /equipper/picture/upload`   | `POST /provider/profile/photo/upload` | Upload provider photo  |
| `GET /equipper/equipments`        | `GET /provider/equipment`             | Get provider equipment |
| `GET /equipper/equipments/booked` | `GET /provider/equipment/booked`      | Get booked equipment   |
| `GET /equipper/booked`            | `GET /provider/bookings`              | Get provider bookings  |

### Authenticated Renter Routes (formerly Lodger)

| Legacy Route                  | New Route                           | Description             |
| ----------------------------- | ----------------------------------- | ----------------------- |
| `GET /lodger`                 | `GET /renter`                       | Get current renter      |
| `GET /lodger/:id`             | `GET /renter/:id`                   | Get renter by ID        |
| `GET /lodger/all`             | `GET /renter/all`                   | Get all renters         |
| `GET /lodger/all/:country`    | `GET /renter/all/:country`          | Get renters by country  |
| `PUT /lodger`                 | `PUT /renter`                       | Update renter           |
| `DELETE /lodger`              | `DELETE /renter`                    | Delete renter           |
| `POST /lodger/picture/upload` | `POST /renter/profile/photo/upload` | Upload renter photo     |
| `GET /lodger/booking`         | `GET /renter/bookings`              | Get renter bookings     |
| `GET /lodger/booked/`         | `GET /renter/booked`                | Get renter booked items |

### Team Management Routes

| Legacy Route                                       | New Route                                         | Description               |
| -------------------------------------------------- | ------------------------------------------------- | ------------------------- |
| `GET /lodger/member`                               | `GET /renter/team/members`                        | Get renter team members   |
| `GET /equipper/member`                             | `GET /provider/team/members`                      | Get provider team members |
| `POST /member`                                     | `POST /team/member`                               | Add team member           |
| `PUT /member/:member_id`                           | `PUT /team/member/:id`                            | Update team member        |
| `GET /member/:member_id/:member_of`                | `GET /team/member/:id/:member_of`                 | Get team member           |
| `GET /member/selection_list/:member_id/:member_of` | `GET /team/member/selection-list/:id/:member_of`  | Get member selection list |
| `DELETE /member`                                   | `DELETE /team/member`                             | Delete team member        |
| `GET /member/verify_email/:email/:member_of`       | `GET /team/member/verify-email/:email/:member_of` | Verify member email       |

### Project Management Routes

| Legacy Route                                          | New Route                                 | Description                 |
| ----------------------------------------------------- | ----------------------------------------- | --------------------------- |
| `PUT /project/:project_id`                            | `PUT /project/:id`                        | Update project              |
| `DELETE /project/:project_id`                         | `DELETE /project/:id`                     | Delete project              |
| `POST /project/affect_one_equipment/:project_id`      | `POST /project/:id/assign-equipment`      | Assign equipment to project |
| `POST /project/affect_one_bidz_equipment/:project_id` | `POST /project/:id/assign-bidz-equipment` | Assign bidz equipment       |
| `POST /project/affect_one_member/:project_id`         | `POST /project/:id/assign-member`         | Assign member to project    |
| `GET /project/:project_id`                            | `GET /project/:id`                        | Get project by ID           |

## URL Constants Changes (src/shared/helpers/Url_constants.js)

The URL constants file has been completely refactored with:

- New kebab-case constants following consistent naming patterns
- Legacy constant mappings for backward compatibility
- Logical grouping by functionality (Authentication, Equipment, Provider, Renter, etc.)

### Example Constant Mappings

```javascript
// New constants
const AUTH_SIGNUP = '/public/auth/signup';
const PROVIDER_EQUIPMENT_LIST = '/provider/equipment';
const RENTER_INFO = '/renter';

// Legacy mappings for backward compatibility
const SIGNUP = AUTH_SIGNUP;
const GET_EQUIPPER_INFO = PROVIDER_INFO;
const GET_LODGER_INFO = RENTER_INFO;
```

## Parameter Standardization

### Route Parameters

- Standardized parameter naming: `:id` instead of mixed `:equipment_id`, `:equipper_id`, etc.
- Consistent parameter patterns across similar routes
- Maintained parameter functionality while improving naming consistency

### Query Parameters

- No changes to query parameter handling
- All existing query parameter functionality preserved

## Backward Compatibility

### Frontend Compatibility

All legacy routes are preserved with `Navigate` redirects to new routes:

```jsx
<Route
  path="/equipperManagementPortal/*"
  element={<Navigate to="/provider-management-portal" replace />}
/>
```

### Backend Compatibility

All legacy API endpoints are maintained alongside new endpoints:

```go
// New route
authenticatedHandlers.GET("/provider", getEquipper(s.service))

// Legacy route (maintained for compatibility)
authenticatedHandlers.GET("/equipper", getEquipper(s.service))
```

## Migration Timeline

1. **Phase 1**: New routes implemented alongside legacy routes
2. **Phase 2**: Frontend updated to use new routes with legacy fallbacks
3. **Phase 3**: Backend updated with new endpoints and legacy compatibility
4. **Phase 4**: Documentation and testing completed
5. **Future Phase**: Legacy routes can be deprecated after sufficient transition period

## Testing Considerations

- All existing functionality should work without changes
- New routes should provide identical functionality to legacy routes
- Navigation and deep linking should work for both old and new routes
- API responses should be identical between legacy and new endpoints

## Notes

- This refactoring is **non-breaking** and maintains full backward compatibility
- Legacy routes will continue to work indefinitely
- New development should use the new route patterns
- The refactoring establishes a foundation for consistent routing patterns going forward
