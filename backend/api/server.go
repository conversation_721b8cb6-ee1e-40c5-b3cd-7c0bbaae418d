package api

import (
	"context"
	"fmt"
	"net/http"
	"time"

	"github.com/gin-gonic/gin"

	"github.com/vima-inc/derental/api/middleware"
	"github.com/vima-inc/derental/service"
)

// Server is a struct representing a http Server.
type Server struct {
	httpServer *http.Server
	service    *service.Service
	handler    *gin.Engine
}

// New creates a new Server instance.
func New(port string, service *service.Service, options ...Options) *Server {
	handler := gin.Default()
	handler.Use(middleware.Errors())

	// Configure trusted proxies for Cloud Run
	// Trust all proxies in development, in production this should be restricted to Google's IP ranges
	if err := handler.SetTrustedProxies([]string{"0.0.0.0/0"}); err != nil {
		fmt.Printf("Warning: Failed to set trusted proxies: %v\n", err)
	}

	s := &Server{
		service: service,
		httpServer: &http.Server{
			Addr: fmt.Sprintf(":%s", port),
		},
		handler: handler,
	}

	for _, o := range options {
		o(s)
	}

	return s
}

// StartHTTP start the http server.
func (s *Server) StartHTTP() error {
	s.httpServer.Handler = s.handler

	return s.httpServer.ListenAndServe()
}

// Shutdown shutdown the http server.
func (s *Server) Shutdown(ctx context.Context) error {
	return s.httpServer.Shutdown(ctx)
}

// WithDefaultRoutes sets the default routes.
func WithDefaultRoutes(auth gin.HandlerFunc) Options {
	return func(s *Server) {
		// ===== PUBLIC API ROUTES (New kebab-case naming) =====
		public := s.handler.Group("/public").Use(middleware.Latency())
		{
			// Authentication endpoints
			public.POST("/auth/signin", signin(s.service))
			public.POST("/auth/signup", signup(s.service))
			public.GET("/auth/user/exists/:email", existByEmail(s.service))
			public.
				Use(middleware.CacheControl(5*time.Second)).
				GET("/auth/password/forgot/:email", forgotPassword(s.service))
			public.POST("/auth/password/validate", validateToken(s.service))
			public.POST("/auth/password/reset", resetPassword(s.service))

			// Equipment endpoints
			public.
				Use(middleware.CacheControl(100*time.Second)).
				GET("/equipment/:id", getEquipment(s.service))
			public.GET("/equipment/by-provider", getEquipmentsByEquipperIDAndEquipmentName(s.service))

			// Provider endpoints (formerly equipper)
			public.
				Use(middleware.CacheControl(100*time.Second)).
				GET("/provider/:id", getEquipperByID(s.service))
			public.GET("/provider/email/:email", getEquipperByEmail(s.service))
			public.GET("/provider/username/:username", getEquipperByUserName(s.service))
			public.
				Use(middleware.CacheControl(2*time.Second)).
				GET("/provider/by-category", getAllEquippersByCategory(s.service))
			public.GET("/providers", getAllEquipper(s.service))
			public.GET("/providers/by-country/:country", getEquippersByCountry(s.service))

			// Derental Bidz Equipment
			public.GET("/derental-bidz-equipment/:id", getBidzEquipmentByID(s.service))
			// Legacy tooler route for backward compatibility
			public.GET("/tooler-bidz-equipment/:id", getBidzEquipmentByID(s.service))

			// Miscellaneous
			public.POST("/lead", addLead(s.service))
			public.POST("/order/complete", handleOrderCompleteWebhook(s.service))
			public.POST("/order/notify", handleOrderCompleteNotification(s.service))
			public.GET("/countries", getAllCountries())

			// ===== LEGACY PUBLIC ROUTES (for backward compatibility) =====
			public.POST("/signin", signin(s.service))
			public.POST("/signup", signup(s.service))
			public.GET("/exist/:email", existByEmail(s.service))
			public.
				Use(middleware.CacheControl(5*time.Second)).
				GET("/password/forgot/:email", forgotPassword(s.service))
			public.POST("/password/validate", validateToken(s.service))
			public.POST("/password/reset", resetPassword(s.service))
			public.
				Use(middleware.CacheControl(100*time.Second)).
				GET("/equipment/:equipment_id", getEquipment(s.service))
			public.
				Use(middleware.CacheControl(100*time.Second)).
				GET("/equipper/:equipper_id", getEquipperByID(s.service))
			public.GET("/equipper/email/:email", getEquipperByEmail(s.service))
			public.GET("/tooler_bidz_equipment/:tooler_bidz_equipment_id", getBidzEquipmentByID(s.service))
			public.
				Use(middleware.CacheControl(2*time.Second)).
				GET("/equipper/category", getAllEquippersByCategory(s.service))
			public.GET("/equipment/equipper", getEquipmentsByEquipperIDAndEquipmentName(s.service))
			public.GET("/equipper/user_name/:userName", getEquipperByUserName(s.service))
			public.GET("/equippers", getAllEquipper(s.service))
			public.GET("/equippers/country/:country", getEquippersByCountry(s.service))
			public.GET("/country", getAllCountries())
		}

		// ===== AUTHENTICATED API ROUTES (New kebab-case naming) =====
		authenticatedHandlers := s.handler.Group("/").Use(middleware.Latency()).Use(auth)
		{
			// Provider Management (formerly equipper)
			authenticatedHandlers.GET("/provider", getEquipper(s.service))
			authenticatedHandlers.PUT("/provider", updateEquipper(s.service))
			authenticatedHandlers.DELETE("/provider", deleteEquipper(s.service))
			authenticatedHandlers.POST("/provider/profile/photo/upload", uploadProfilePicture(s.service))
			authenticatedHandlers.
				Use(middleware.CacheControl(2*time.Second)).
				GET("/provider/equipment", getEquipmentsByEquipperID(s.service))
			authenticatedHandlers.
				Use(middleware.CacheControl(2*time.Second)).
				GET("/provider/equipment/booked", getBookedEquipmentsByEquipperID(s.service))
			authenticatedHandlers.
				Use(middleware.CacheControl(2*time.Second)).
				GET("/provider/bookings", getBookedEquipmentByStatusAndEquipperID(s.service))

			// Renter Management (formerly lodger)
			authenticatedHandlers.
				Use(middleware.CacheControl(2*time.Second)).
				GET("/renter", getLodger(s.service))
			authenticatedHandlers.Use(middleware.CacheControl(2*time.Second)).GET(
				"/renter/:id", getLodgerByID(s.service),
			)
			authenticatedHandlers.GET("/renter/all", getAllLodgers(s.service))
			authenticatedHandlers.GET("/renter/all/:country", GetAllLodgersByCountry(s.service))
			authenticatedHandlers.PUT("/renter", updateLodger(s.service))
			authenticatedHandlers.DELETE("/renter", deleteLodger(s.service))
			authenticatedHandlers.POST("/renter/profile/photo/upload", uploadLodgerProfilePicture(s.service))
			authenticatedHandlers.
				Use(middleware.CacheControl(2*time.Second)).
				GET("/renter/bookings", getBookEquipmentByLodgerID(s.service))
			authenticatedHandlers.
				Use(middleware.CacheControl(2*time.Second)).
				GET("/renter/booked", getBookedEquipmentByStatusAndLodgerID(s.service))

			// ===== LEGACY AUTHENTICATED ROUTES (for backward compatibility) =====
			authenticatedHandlers.GET("/equipper", getEquipper(s.service))
			authenticatedHandlers.PUT("/equipper", updateEquipper(s.service))
			authenticatedHandlers.DELETE("/equipper", deleteEquipper(s.service))
			authenticatedHandlers.POST("/equipper/picture/upload", uploadProfilePicture(s.service))
			authenticatedHandlers.POST("/lodger/picture/upload", uploadLodgerProfilePicture(s.service))
			authenticatedHandlers.
				Use(middleware.CacheControl(2*time.Second)).
				GET("/equipper/equipments", getEquipmentsByEquipperID(s.service))
			authenticatedHandlers.
				Use(middleware.CacheControl(2*time.Second)).
				GET("/equipper/equipments/booked", getBookedEquipmentsByEquipperID(s.service))
			authenticatedHandlers.
				Use(middleware.CacheControl(2*time.Second)).
				GET("/equipper/booked", getBookedEquipmentByStatusAndEquipperID(s.service))
			authenticatedHandlers.
				Use(middleware.CacheControl(2*time.Second)).
				GET("/lodger", getLodger(s.service))
			authenticatedHandlers.Use(middleware.CacheControl(2*time.Second)).GET(
				"/lodger/:id", getLodgerByID(s.service),
			)
			authenticatedHandlers.GET("/lodger/all", getAllLodgers(s.service))
			authenticatedHandlers.GET("/lodger/all/:country", GetAllLodgersByCountry(s.service))
			authenticatedHandlers.PUT("/lodger", updateLodger(s.service))
			authenticatedHandlers.DELETE("/lodger", deleteLodger(s.service))
			authenticatedHandlers.
				Use(middleware.CacheControl(2*time.Second)).
				GET("/lodger/booking", getBookEquipmentByLodgerID(s.service))
			authenticatedHandlers.
				Use(middleware.CacheControl(2*time.Second)).
				GET("/lodger/booked/", getBookedEquipmentByStatusAndLodgerID(s.service))

			// Team Management
			authenticatedHandlers.
				Use(middleware.CacheControl(2*time.Second)).
				GET("/renter/team/members", getMembersLodger(s.service))
			authenticatedHandlers.
				Use(middleware.CacheControl(2*time.Second)).
				GET("/provider/team/members", getMembersEquipper(s.service))
			authenticatedHandlers.POST("/team/member", addMember(s.service))
			authenticatedHandlers.PUT("/team/member/:id", updateMember(s.service))
			authenticatedHandlers.GET("/team/member/:id/:member_of", getMemberByID(s.service))
			authenticatedHandlers.GET("/team/member/selection-list/:id/:member_of", getSelectionList(s.service))
			authenticatedHandlers.DELETE("/team/member", deleteMember(s.service))
			authenticatedHandlers.GET("/team/member/verify-email/:email/:member_of", invitationExists(s.service))

			// Project Management
			authenticatedHandlers.GET("/projects", getProjectByLodgerID(s.service))
			authenticatedHandlers.POST("/project", addProject(s.service))
			authenticatedHandlers.PUT("/project/:id", updateProject(s.service))
			authenticatedHandlers.DELETE("/project/:id", deleteProject(s.service))
			authenticatedHandlers.POST("/project/:id/assign-equipment", affectOneEquipment(s.service))
			authenticatedHandlers.POST("/project/:id/assign-bidz-equipment", affectOneBidzEquipment(s.service))
			authenticatedHandlers.POST("/project/:id/assign-member", affectOneMember(s.service))
			authenticatedHandlers.GET("/project/:id", getProjectByID(s.service))

			// ===== LEGACY TEAM & PROJECT ROUTES =====
			authenticatedHandlers.
				Use(middleware.CacheControl(2*time.Second)).
				GET("/lodger/member", getMembersLodger(s.service))
			authenticatedHandlers.
				Use(middleware.CacheControl(2*time.Second)).
				GET("/equipper/member", getMembersEquipper(s.service))
			authenticatedHandlers.POST("/member", addMember(s.service))
			authenticatedHandlers.PUT("/member/:member_id", updateMember(s.service))
			authenticatedHandlers.GET("/member/:member_id/:member_of", getMemberByID(s.service))
			authenticatedHandlers.GET("/member/selection_list/:member_id/:member_of", getSelectionList(s.service))
			authenticatedHandlers.DELETE("/member", deleteMember(s.service))
			authenticatedHandlers.GET("/member/verify_email/:email/:member_of", invitationExists(s.service))
			authenticatedHandlers.PUT("/project/:project_id", updateProject(s.service))
			authenticatedHandlers.DELETE("/project/:project_id", deleteProject(s.service))
			authenticatedHandlers.POST("/project/affect_one_equipment/:project_id", affectOneEquipment(s.service))
			authenticatedHandlers.POST("/project/affect_one_bidz_equipment/:project_id", affectOneBidzEquipment(s.service))
			authenticatedHandlers.POST("/project/affect_one_member/:project_id", affectOneMember(s.service))
			authenticatedHandlers.GET("/project/:project_id", getProjectByID(s.service))

			// Equipment Management
			authenticatedHandlers.POST("/equipment/bulk-upload", uploadEquipments(s.service))
			authenticatedHandlers.POST("/equipment", addEquipment(s.service))
			authenticatedHandlers.POST("/equipment/:id/upload-image", uploadImageEquipment(s.service))
			authenticatedHandlers.PUT("/equipment/:id", updateEquipment(s.service))
			authenticatedHandlers.PUT("/equipment/batch-update", updateBatchOfEquipmentByName(s.service))
			authenticatedHandlers.DELETE("/equipment/:id", deleteEquipment(s.service))
			authenticatedHandlers.DELETE("/equipment/all", deleteEquipmentsByEquipperID(s.service))
			authenticatedHandlers.POST("/equipment/sync", airTableSynchronization(s.service))
			authenticatedHandlers.POST("/equipment/:id/status", changeEquipmentStatus(s.service))

			// Equipment Booking
			authenticatedHandlers.POST("/equipment/booking", bookEquipment(s.service))
			authenticatedHandlers.POST("/equipment/booking/:id/accept", acceptBookEquipment(s.service))
			authenticatedHandlers.POST("/equipment/booking/:id/reject", rejectBookEquipment(s.service))
			authenticatedHandlers.POST("/equipment/booking/:id/cancel", cancelBookEquipment(s.service))
			authenticatedHandlers.POST("/equipment/booking/:id/return", updateBookingEquipmentStatusAfterReturnEquipment(s.service))

			// ===== LEGACY EQUIPMENT ROUTES =====
			authenticatedHandlers.POST("/equipments/upload", uploadEquipments(s.service))
			authenticatedHandlers.POST("/equipment/upload_image/:equipment_id", uploadImageEquipment(s.service))
			authenticatedHandlers.PUT("/equipment/:equipment_id", updateEquipment(s.service))
			authenticatedHandlers.PUT("/equipment", updateBatchOfEquipmentByName(s.service))
			authenticatedHandlers.DELETE("/equipment/:equipment_id", deleteEquipment(s.service))
			authenticatedHandlers.POST("/equipment/synchronization", airTableSynchronization(s.service))
			authenticatedHandlers.POST("/equipment/:equipment_id", changeEquipmentStatus(s.service))
			authenticatedHandlers.POST("/equipment/book", bookEquipment(s.service))
			authenticatedHandlers.POST("/equipment/book/:book_equipment_id/accept", acceptBookEquipment(s.service))
			authenticatedHandlers.POST("/equipment/book/:book_equipment_id/reject", rejectBookEquipment(s.service))
			authenticatedHandlers.POST("/equipment/book/:book_equipment_id/cancel", cancelBookEquipment(s.service))
			authenticatedHandlers.POST("/equipment/book/:book_equipment_id/return", updateBookingEquipmentStatusAfterReturnEquipment(s.service))

			// Bid Requests
			authenticatedHandlers.POST("/bid-requests", sendBitsRequest(s.service))
			authenticatedHandlers.POST("/bid-requests/:id/accept", acceptBidsRequest(s.service))
			authenticatedHandlers.POST("/bid-requests/:id/reject", rejectBidsRequest(s.service))
			authenticatedHandlers.
				Use(middleware.CacheControl(2*time.Second)).
				GET("/bid-requests", getAllBidsRequest(s.service))
			authenticatedHandlers.
				Use(middleware.CacheControl(2*time.Second)).
				GET("/bid-requests/provider", getAllBidsRequestByEquipperID(s.service))
			authenticatedHandlers.
				Use(middleware.CacheControl(2*time.Second)).
				GET("/bid-requests/renter", getAllBidsRequestByLodgerID(s.service))
			authenticatedHandlers.
				Use(middleware.CacheControl(2*time.Second)).
				GET("/bid-requests/:id", getBidsRequestID(s.service))

			// ===== LEGACY BID REQUEST ROUTES =====
			authenticatedHandlers.POST("/request_bids/send_bits_request", sendBitsRequest(s.service))
			authenticatedHandlers.POST("/request_bids/:bids_request_id/accept", acceptBidsRequest(s.service))
			authenticatedHandlers.POST("/request_bids/:bids_request_id/reject", rejectBidsRequest(s.service))
			authenticatedHandlers.
				Use(middleware.CacheControl(2*time.Second)).
				GET("/request_bids", getAllBidsRequest(s.service))
			authenticatedHandlers.
				Use(middleware.CacheControl(2*time.Second)).
				GET("/request_bids/equipper", getAllBidsRequestByEquipperID(s.service))
			authenticatedHandlers.
				Use(middleware.CacheControl(2*time.Second)).
				GET("/request_bids/lodger", getAllBidsRequestByLodgerID(s.service))
			authenticatedHandlers.
				Use(middleware.CacheControl(2*time.Second)).
				GET("/request_bids/:request_id", getBidsRequestID(s.service))
			// Credit Check Forms
			authenticatedHandlers.POST("/credit-check-form", addCreditCheckForm(s.service))
			authenticatedHandlers.GET("/credit-check-form/:id", getCreditCheckFormByID(s.service))
			authenticatedHandlers.GET("/credit-check-form", getCreditCheckFormByLodgerID(s.service))
			authenticatedHandlers.DELETE("/credit-check-form/:id", deleteCreditCheckForm(s.service))
			authenticatedHandlers.PUT("/credit-check-form/:id", updateCreditCheckForm(s.service))
			authenticatedHandlers.POST("/credit-check-form/:id/upload", uploadCreditCheckForm(s.service))
			authenticatedHandlers.POST("/credit-check-form/:id/upload/:attachment_name", uploadPDF(s.service))
			authenticatedHandlers.GET("/credit-check-form/:id/attachment/:attachment_name/:booking_id", getCreditCheckFormAttachment(s.service))

			// Bid Offers
			authenticatedHandlers.POST("/bid-offers", addOfferRequest(s.service))
			authenticatedHandlers.POST("/bid-offers/:id/accept", acceptBidsOffer(s.service))
			authenticatedHandlers.POST("/bid-offers/:id/reject", rejectBidsOffer(s.service))
			authenticatedHandlers.POST("/bid-offers/:id/cancel", cancelBidzOffer(s.service))
			authenticatedHandlers.
				Use(middleware.CacheControl(2*time.Second)).
				GET("/bid-offers", getAllBidsOffer(s.service))
			authenticatedHandlers.
				Use(middleware.CacheControl(2*time.Second)).
				GET("/bid-offers/provider", getAllBidsOfferByEquipperID(s.service))
			authenticatedHandlers.
				Use(middleware.CacheControl(2*time.Second)).
				GET("/bid-offers/renter", getAllBidsOfferByLodgerID(s.service))
			authenticatedHandlers.
				Use(middleware.CacheControl(2*time.Second)).
				GET("/bid-offers/:id", getBidsOfferID(s.service))

			// Derental Bidz Equipment
			authenticatedHandlers.POST("/derental-bidz-equipment", addToolerBidzEquipment(s.service))
			authenticatedHandlers.GET("/derental-bidz-equipment", getAllToolerBidzEquipment(s.service))
			authenticatedHandlers.PUT("/derental-bidz-equipment", updateToolerBidzEquipment(s.service))
			authenticatedHandlers.DELETE("/derental-bidz-equipment/:id", deleteToolerBidzEquipment(s.service))
			authenticatedHandlers.POST("/derental-bidz-equipment/:id/upload-photo", uploadEquipmentPhoto(s.service))
			authenticatedHandlers.DELETE("/derental-bidz-equipment/all", dropToolerBidzEquipments(s.service))

			// Legacy tooler routes for backward compatibility
			authenticatedHandlers.POST("/tooler-bidz-equipment", addToolerBidzEquipment(s.service))
			authenticatedHandlers.GET("/tooler-bidz-equipment", getAllToolerBidzEquipment(s.service))
			authenticatedHandlers.PUT("/tooler-bidz-equipment", updateToolerBidzEquipment(s.service))
			authenticatedHandlers.DELETE("/tooler-bidz-equipment/:id", deleteToolerBidzEquipment(s.service))
			authenticatedHandlers.POST("/tooler-bidz-equipment/:id/upload-photo", uploadEquipmentPhoto(s.service))
			authenticatedHandlers.DELETE("/tooler-bidz-equipment/all", dropToolerBidzEquipments(s.service))

			// Promotions
			authenticatedHandlers.POST("/promotion", createPromotionCode(s.service))
			authenticatedHandlers.DELETE("/promotion/:code", deletePromotionCode(s.service))
			authenticatedHandlers.GET("/promotion", getAllPromotionCodeByEquipperID(s.service))
			authenticatedHandlers.GET("/promotion/:provider_id", getPromotionCodeByEquipperIDAndLodgerID(s.service))

			// ===== LEGACY ROUTES FOR BACKWARD COMPATIBILITY =====
			authenticatedHandlers.POST("/credit_check_form/add", addCreditCheckForm(s.service))
			authenticatedHandlers.GET("/credit_check_form/:credit_check_form_id", getCreditCheckFormByID(s.service))
			authenticatedHandlers.GET("/credit_check_form", getCreditCheckFormByLodgerID(s.service))
			authenticatedHandlers.DELETE("/credit_check_form/:credit_check_form_id", deleteCreditCheckForm(s.service))
			authenticatedHandlers.PUT("/credit_check_form/:credit_check_form_id", updateCreditCheckForm(s.service))
			authenticatedHandlers.POST("/credit_check_form/upload_credit_check_form/:credit_check_form_id", uploadCreditCheckForm(s.service))
			authenticatedHandlers.POST("/credit_check_form/upload_credit_check_form/:credit_check_form_id/:attachment_name", uploadPDF(s.service))
			authenticatedHandlers.GET("/credit_check_form/attachment/:credit_check_form_id/:attachment_name/:booking_id", getCreditCheckFormAttachment(s.service))
			authenticatedHandlers.POST("/offer_bids/send_bits_offer", addOfferRequest(s.service))
			authenticatedHandlers.POST("/offer_bids/:bids_offer_id/accept", acceptBidsOffer(s.service))
			authenticatedHandlers.POST("/offer_bids/:bids_offer_id/reject", rejectBidsOffer(s.service))
			authenticatedHandlers.POST("/offer_bids/:bids_offer_id/cancel", cancelBidzOffer(s.service))
			authenticatedHandlers.
				Use(middleware.CacheControl(2*time.Second)).
				GET("/offer_bids", getAllBidsOffer(s.service))
			authenticatedHandlers.
				Use(middleware.CacheControl(2*time.Second)).
				GET("/offer_bids/equipper", getAllBidsOfferByEquipperID(s.service))
			authenticatedHandlers.
				Use(middleware.CacheControl(2*time.Second)).
				GET("/offer_bids/lodger", getAllBidsOfferByLodgerID(s.service))
			authenticatedHandlers.
				Use(middleware.CacheControl(2*time.Second)).
				GET("/offer_bids/:offer_id", getBidsOfferID(s.service))
			authenticatedHandlers.POST("/tooler_bidz_equipment", addToolerBidzEquipment(s.service))
			authenticatedHandlers.GET("/tooler_bidz_equipment", getAllToolerBidzEquipment(s.service))
			authenticatedHandlers.PUT("/tooler_bidz_equipment", updateToolerBidzEquipment(s.service))
			authenticatedHandlers.DELETE("/tooler_bidz_equipment/:tooler_bidz_equipment_id", deleteToolerBidzEquipment(s.service))
			authenticatedHandlers.POST("/tooler_bidz_equipment/:tooler_bidz_equipment_id", uploadEquipmentPhoto(s.service))
			authenticatedHandlers.DELETE("/tooler_bidz_equipment/all", dropToolerBidzEquipments(s.service))
			authenticatedHandlers.GET("/promotion/:equipper_id", getPromotionCodeByEquipperIDAndLodgerID(s.service))
		}
	}
}

// WithPubSub sets the pubsub handler.
func WithPubSub() Options {
	return func(s *Server) {
		s.handler.POST("/", uploadEquipmentsFromFileNotification(s.service))
	}
}

func WithCron() Options {
	return func(s *Server) {
		s.handler.POST("/", processEquipmentBookingsAndNotifyEquippers(s.service))
	}
}
