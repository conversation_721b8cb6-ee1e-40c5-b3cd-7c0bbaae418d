// ===== NEW ROUTING CONSTANTS (Following kebab-case convention) =====

// Authentication & User Management
const AUTH_SIGNUP = '/public/auth/signup';
const AUTH_SIGNIN = '/public/auth/signin';
const AUTH_PASSWORD_VALIDATE = '/public/auth/password/validate';
const AUTH_PASSWORD_FORGOT = '/public/auth/password/forgot/';
const AUTH_PASSWORD_RESET = '/public/auth/password/reset';

// ===== LEGACY CONSTANTS (Deprecated - use new constants above) =====
const SIGNUP = AUTH_SIGNUP;
const SIGNIN = AUTH_SIGNIN;
const GET_VERIFICATION_CODE = AUTH_PASSWORD_VALIDATE;
const FIND_USER_BY_EMAIL = AUTH_PASSWORD_FORGOT;
const RESET_PASSWORD = AUTH_PASSWORD_RESET;
// Equipment Management
const EQUIPMENT_BY_ID = '/public/equipment/';
const EQUIPMENT_UPLOAD = '/equipment/upload';
const EQUIPMENT = '/equipment';
const EQUIPMENT_UPLOAD_IMAGE = '/equipment/upload-image';
const EQUIPMENT_BOOKING_STATUS = '/equipment/booking-status';
const EQUIPMENT_BOOKING = '/equipment/booking';
const EQUIPMENT_SYNCHRONIZATION = '/equipment/synchronization';
const EQUIPMENT_DELETE_ALL = '/equipment/all';

// Provider (formerly Equipper) Management
const PROVIDER_EQUIPMENT_LIST = '/provider/equipment';
const PROVIDER_RENTALS_ACTIVE = '/provider/rentals/active';
const PROVIDER_INFO = '/provider';
const PROVIDER_REQUESTS = '/provider/requests';
const PROVIDER_BY_ID = '/public/provider/';
const PROVIDER_BY_EMAIL = '/public/provider/email/';
const PROVIDER_BY_USERNAME = '/public/provider/username/';
const PROVIDER_PROFILE_PHOTO_UPLOAD = '/provider/profile/photo/upload';

// Legacy Equipment Constants
const VERIFY_CREDIT_CHECK_FORM = '/credit-check/verify';
const GET_EQUIPMENT_BY_ID = EQUIPMENT_BY_ID;
const ADD_EQUIPMENT = EQUIPMENT_UPLOAD;
const EQUIPMENT_UPLOAD_IMAGE_LEGACY = EQUIPMENT_UPLOAD_IMAGE;
const UPDATE_BOOKING_STATUS = EQUIPMENT_BOOKING_STATUS;
const GET_ALL_EQUIPMENTS = PROVIDER_EQUIPMENT_LIST + '?';
const GET_RENTALS_IN_PROGRESS = PROVIDER_RENTALS_ACTIVE;
// Renter (formerly Lodger) Management
const RENTER_INFO = '/renter';
const RENTER_REQUESTS = '/renter/requests/';
const RENTER_RENTALS_SUMMARY = '/renter/rentals/summary/';
const RENTER_PROFILE_PHOTO_UPLOAD = '/renter/profile/photo/upload';
const RENTER_ALL = '/renter/all';

// Team Management
const TEAM_MEMBER = '/team/member';
const TEAM_MEMBER_BY_ID = '/team/member/';
const TEAM_MEMBER_VERIFY_EMAIL = '/team/member/verify-email/';
const TEAM_MEMBER_SELECTION_LIST = '/team/member/selection-list/';
const TEAM_PROVIDER = '/team/provider';
const TEAM_RENTER = '/team/renter';

// Project Management
const PROJECT_LIST = '/projects';
const PROJECT = '/project';

// Promotions & Marketing
const PROMOTION = '/promotion';
const EQUIPMENT_PROMOTION = '/equipment/promotion';

// Credit Management
const CREDIT_CHECK_FORM_ADD = '/credit-check-form/add';
const CREDIT_CHECK_FORM_GET = '/credit-check-form';
const CREDIT_CHECK_FORM_UPLOAD = '/credit-check-form/upload';

// Bidding System
const BIDZ_REQUEST_SEND = '/bidz-request/send';
const BIDZ_REQUEST_BY_RENTER = '/bidz-request/renter';
const BIDZ_REQUEST_BY_PROVIDER = '/bidz-request/provider';
const BIDZ_REQUEST = '/bidz-request/';
const BIDZ_OFFER_SEND = '/bidz-offer/send';
const BIDZ_OFFER_BY_RENTER = '/bidz-offer/renter';
const BIDZ_OFFER_BY_PROVIDER = '/bidz-offer/provider';
const BIDZ_OFFER = '/bidz-offer/';

// Derental Bidz Inventory
const DERENTAL_BIDZ_INVENTORY = '/derental-bidz-equipment';
const DERENTAL_BIDZ_INVENTORY_PUBLIC = '/public/derental-bidz-equipment';

// Miscellaneous
const LEAD_SEND = '/public/lead';
const IP_INFO = '/public/ip-info';
const IMG_BASE_URL = import.meta.env
  .VITE_DERENTAL_BIDZ_STORAGE_EQUIPMENTS_IMG_PATH;

// External Mock APIs (Analytics - to be replaced with internal endpoints)
const ANALYTICS_COMMENTS =
  'https://47597e85-f560-42e2-b996-525a500e4e55.mock.pstmn.io/comments';
const ANALYTICS_RATE_RENTER =
  'https://d80ed529-d502-4e2b-a730-5a465a2ff426.mock.pstmn.io/rate-renter';
const ANALYTICS_EQUIPMENT_PROMOTION =
  'https://bb0c5d92-1d00-4844-a6cf-3f109a117089.mock.pstmn.io/equipment-promotion';
const ANALYTICS_CREDIT_CARD_ADD =
  'https://6922e6fc-20dc-44bb-a7b0-b0aa6b4a7148.mock.pstmn.io/credit-card-add';
const ANALYTICS_TOTAL_RENTED_EQUIPMENT =
  'https://666238b5-c4ba-4372-9851-47ff2dccdf2a.mock.pstmn.io/total-rented-equipment';
const ANALYTICS_TOTAL_RENTED_INCOME =
  'https://666238b5-c4ba-4372-9851-47ff2dccdf2a.mock.pstmn.io/total-rented-income';
const ANALYTICS_TOP_RENTED_EQUIPMENT =
  'https://666238b5-c4ba-4372-9851-47ff2dccdf2a.mock.pstmn.io/top-rented-equipment';
const ANALYTICS_TOP_RENTED_EQUIPMENT_BY_LOCATION =
  'https://be46d772-4156-406b-98ef-ae6af1501126.mock.pstmn.io/top-rented-equipment-by-location';
const ANALYTICS_TOP_RENTERS =
  'https://666238b5-c4ba-4372-9851-47ff2dccdf2a.mock.pstmn.io/top-renters';
const ANALYTICS_RENTAL_HISTORY =
  'https://666238b5-c4ba-4372-9851-47ff2dccdf2a.mock.pstmn.io/rental-history';
// ===== LEGACY CONSTANT MAPPINGS (Deprecated - use new constants above) =====

// Provider (Equipper) Legacy Mappings
const GET_EQUIPPER_INFO = PROVIDER_INFO;
const GET_EQUIPPER_REQUESTS = PROVIDER_REQUESTS;
const UPDATE_EQUIPPER_INFO = PROVIDER_INFO;
const UPLOAD_PROFILE_PHOTO_EQUIPPER = PROVIDER_PROFILE_PHOTO_UPLOAD;
const GET_EQUIPPER_BY_ID = PROVIDER_BY_ID;
const GET_EQUIPPER_BY_EMAIL = PROVIDER_BY_EMAIL;
const GET_EQUIPPER_BY_USER_NAME = PROVIDER_BY_USERNAME;
const GET_TEAM_EQUIPPER = TEAM_PROVIDER;

// Renter (Lodger) Legacy Mappings
const GET_LODGER_INFO = RENTER_INFO;
const GET_LODGER_REQUESTS = RENTER_REQUESTS;
const UPDATE_LODGER_INFO = RENTER_INFO;
const GET_ALL_LODGERS = RENTER_ALL;
const GET_RENTAL_IN_SUMMARY = RENTER_RENTALS_SUMMARY;
const UPLOAD_PROFILE_PHOTO_LODGER = RENTER_PROFILE_PHOTO_UPLOAD;
const GET_TEAM_LODGER = TEAM_RENTER;

// Equipment Legacy Mappings
const BOOK_EQUIPMENT = EQUIPMENT_BOOKING;
const AIRTABLE_SYNC = EQUIPMENT_SYNCHRONIZATION;
const DELETE_ALL_EQUIPMENTS = EQUIPMENT_DELETE_ALL;

// Project Legacy Mappings
const GET_PROJECT = PROJECT_LIST;

// Team Legacy Mappings
const DELETE_MEMBER = TEAM_MEMBER;
const EDIT_MEMBER = TEAM_MEMBER_BY_ID;
const ADD_MEMBER = TEAM_MEMBER;
const VERIFY_EMAIL = TEAM_MEMBER_VERIFY_EMAIL;
const GET_SELECTION_LIST = TEAM_MEMBER_SELECTION_LIST;

// Credit Check Legacy Mappings
const ADD_CREDIT_CHECK_FORM = CREDIT_CHECK_FORM_ADD;
const GET_MY_CREDIT_CHECK_FORM = CREDIT_CHECK_FORM_GET;
const UPLOAD_MY_CREDIT_CHECK_FORM = CREDIT_CHECK_FORM_UPLOAD;

// Bidding Legacy Mappings
const ADD_BIDZ_REQUEST = BIDZ_REQUEST_SEND;
const GET_BIDZ_OFFER_BY_RENTER_ID = BIDZ_OFFER_BY_RENTER;
const GET_BIDZ_OFFER_BY_EQUIPPER_ID = BIDZ_OFFER_BY_PROVIDER;
const GET_BIDZ_REQUEST_LODGER = BIDZ_REQUEST_BY_RENTER;
const GET_BIDZ_REQUEST_EQUIPPER = BIDZ_REQUEST_BY_PROVIDER;
const SET_BID_OFFER = BIDZ_OFFER_SEND;

// Derental Bidz Legacy Mappings
const PUBLIC_DERENTAL_BIDZ_INVENTORY = DERENTAL_BIDZ_INVENTORY_PUBLIC;
// Legacy tooler references for backward compatibility
const TOOLER_BIDZ_INVENTORY = DERENTAL_BIDZ_INVENTORY;
const TOOLER_BIDZ_INVENTORY_PUBLIC = DERENTAL_BIDZ_INVENTORY_PUBLIC;
const PUBLIC_TOOLER_BIDZ_INVENTORY = DERENTAL_BIDZ_INVENTORY_PUBLIC;

// Miscellaneous Legacy Mappings
const SEND_RECLAMATION = LEAD_SEND;
const GET_IP_INFO = IP_INFO;

// Analytics Legacy Mappings
const GET_COMMENT_BY_ID = ANALYTICS_COMMENTS;
const RATE_LODGER = ANALYTICS_RATE_RENTER;
const GET_EQUIPMENT_PROMOTION = ANALYTICS_EQUIPMENT_PROMOTION;
const ADD_CREDIT_CARD = ANALYTICS_CREDIT_CARD_ADD;
const TOTAL_RENTED_EQUIPMENTS = ANALYTICS_TOTAL_RENTED_EQUIPMENT;
const TOTAL_RENTED_INCOME = ANALYTICS_TOTAL_RENTED_INCOME;
const TOP_RENTED_EQUIPMENTS = ANALYTICS_TOP_RENTED_EQUIPMENT;
const TOP_RENTED_EQUIPMENTS_PER_LOCATION =
  ANALYTICS_TOP_RENTED_EQUIPMENT_BY_LOCATION;
const TOP_LODGERS = ANALYTICS_TOP_RENTERS;
const HISTORY_OF_RENTED_EQUIPMENT = ANALYTICS_RENTAL_HISTORY;

export {
  // ===== NEW ROUTING CONSTANTS (Recommended) =====

  // Authentication
  AUTH_SIGNUP,
  AUTH_SIGNIN,
  AUTH_PASSWORD_VALIDATE,
  AUTH_PASSWORD_FORGOT,
  AUTH_PASSWORD_RESET,

  // Equipment Management
  EQUIPMENT_BY_ID,
  EQUIPMENT_UPLOAD,
  EQUIPMENT,
  EQUIPMENT_UPLOAD_IMAGE,
  EQUIPMENT_BOOKING_STATUS,
  EQUIPMENT_BOOKING,
  EQUIPMENT_SYNCHRONIZATION,
  EQUIPMENT_DELETE_ALL,
  EQUIPMENT_PROMOTION,

  // Provider Management
  PROVIDER_EQUIPMENT_LIST,
  PROVIDER_RENTALS_ACTIVE,
  PROVIDER_INFO,
  PROVIDER_REQUESTS,
  PROVIDER_BY_ID,
  PROVIDER_BY_EMAIL,
  PROVIDER_BY_USERNAME,
  PROVIDER_PROFILE_PHOTO_UPLOAD,

  // Renter Management
  RENTER_INFO,
  RENTER_REQUESTS,
  RENTER_RENTALS_SUMMARY,
  RENTER_PROFILE_PHOTO_UPLOAD,
  RENTER_ALL,

  // Team Management
  TEAM_MEMBER,
  TEAM_MEMBER_BY_ID,
  TEAM_MEMBER_VERIFY_EMAIL,
  TEAM_MEMBER_SELECTION_LIST,
  TEAM_PROVIDER,
  TEAM_RENTER,

  // Project Management
  PROJECT_LIST,
  PROJECT,

  // Promotions
  PROMOTION,

  // Credit Management
  CREDIT_CHECK_FORM_ADD,
  CREDIT_CHECK_FORM_GET,
  CREDIT_CHECK_FORM_UPLOAD,

  // Bidding System
  BIDZ_REQUEST_SEND,
  BIDZ_REQUEST_BY_RENTER,
  BIDZ_REQUEST_BY_PROVIDER,
  BIDZ_REQUEST,
  BIDZ_OFFER_SEND,
  BIDZ_OFFER_BY_RENTER,
  BIDZ_OFFER_BY_PROVIDER,
  BIDZ_OFFER,

  // Derental Bidz
  DERENTAL_BIDZ_INVENTORY,
  DERENTAL_BIDZ_INVENTORY_PUBLIC,

  // Miscellaneous
  LEAD_SEND,
  IP_INFO,
  IMG_BASE_URL,

  // Analytics (External)
  ANALYTICS_COMMENTS,
  ANALYTICS_RATE_RENTER,
  ANALYTICS_EQUIPMENT_PROMOTION,
  ANALYTICS_CREDIT_CARD_ADD,
  ANALYTICS_TOTAL_RENTED_EQUIPMENT,
  ANALYTICS_TOTAL_RENTED_INCOME,
  ANALYTICS_TOP_RENTED_EQUIPMENT,
  ANALYTICS_TOP_RENTED_EQUIPMENT_BY_LOCATION,
  ANALYTICS_TOP_RENTERS,
  ANALYTICS_RENTAL_HISTORY,

  // ===== LEGACY CONSTANTS (Deprecated - for backward compatibility) =====
  SIGNUP,
  SIGNIN,
  GET_VERIFICATION_CODE,
  FIND_USER_BY_EMAIL,
  RESET_PASSWORD,
  VERIFY_CREDIT_CHECK_FORM,
  GET_EQUIPMENT_BY_ID,
  ADD_EQUIPMENT,
  EQUIPMENT_UPLOAD_IMAGE_LEGACY,
  UPDATE_BOOKING_STATUS,
  GET_ALL_EQUIPMENTS,
  GET_RENTALS_IN_PROGRESS,
  GET_EQUIPPER_INFO,
  GET_EQUIPPER_REQUESTS,
  UPDATE_EQUIPPER_INFO,
  UPLOAD_PROFILE_PHOTO_EQUIPPER,
  GET_EQUIPPER_BY_ID,
  GET_EQUIPPER_BY_EMAIL,
  GET_EQUIPPER_BY_USER_NAME,
  GET_TEAM_EQUIPPER,
  GET_LODGER_INFO,
  GET_LODGER_REQUESTS,
  UPDATE_LODGER_INFO,
  GET_ALL_LODGERS,
  GET_RENTAL_IN_SUMMARY,
  UPLOAD_PROFILE_PHOTO_LODGER,
  GET_TEAM_LODGER,
  BOOK_EQUIPMENT,
  AIRTABLE_SYNC,
  DELETE_ALL_EQUIPMENTS,
  GET_PROJECT,
  DELETE_MEMBER,
  EDIT_MEMBER,
  ADD_MEMBER,
  VERIFY_EMAIL,
  GET_SELECTION_LIST,
  ADD_CREDIT_CHECK_FORM,
  GET_MY_CREDIT_CHECK_FORM,
  UPLOAD_MY_CREDIT_CHECK_FORM,
  ADD_BIDZ_REQUEST,
  GET_BIDZ_OFFER_BY_RENTER_ID,
  GET_BIDZ_OFFER_BY_EQUIPPER_ID,
  GET_BIDZ_REQUEST_LODGER,
  GET_BIDZ_REQUEST_EQUIPPER,
  SET_BID_OFFER,
  PUBLIC_DERENTAL_BIDZ_INVENTORY,
  // Legacy tooler exports for backward compatibility
  TOOLER_BIDZ_INVENTORY,
  TOOLER_BIDZ_INVENTORY_PUBLIC,
  PUBLIC_TOOLER_BIDZ_INVENTORY,
  SEND_RECLAMATION,
  GET_IP_INFO,
  GET_COMMENT_BY_ID,
  RATE_LODGER,
  GET_EQUIPMENT_PROMOTION,
  ADD_CREDIT_CARD,
  TOTAL_RENTED_EQUIPMENTS,
  TOTAL_RENTED_INCOME,
  TOP_RENTED_EQUIPMENTS,
  TOP_RENTED_EQUIPMENTS_PER_LOCATION,
  TOP_LODGERS,
  HISTORY_OF_RENTED_EQUIPMENT
};
